const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
const WebSocket = require('ws');
const http = require('http');

const app = express();

const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'usertracking',
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 5,
    queueLimit: 0
});

app.use(express.json());
app.use(cors({ origin: '*' }));

const server = http.createServer(app);
const wss = new WebSocket.Server({ port: 8080 });

const activeUsers = new Map();
const dashboardClients = new Set();
const LOCATIONIQ_TOKEN = '***********************************';

// ✅ NEW: Rate limiting and caching for LocationIQ
const locationCache = new Map(); // Cache coordinates → country
const pendingRequests = new Map(); // Prevent duplicate API calls
let lastApiCall = 0;
const API_RATE_LIMIT = 1000; // 1 second between calls

console.log('🚀 WebSocket server running on port 8080');

wss.on('connection', (ws) => {
    let userId = null;
    let isDashboard = false;
    
    ws.on('message', async (message) => {
        const data = JSON.parse(message);
        
        if (data.type === 'user_online') {
            userId = data.userId;
            
            const userData = {
                ws: ws,
                lastSeen: Date.now(),
                country: 'Unknown',
                city: 'Unknown'
            };
            
            activeUsers.set(userId, userData);
            
            if (data.locationData?.latitude && data.locationData?.longitude) {
                await processLocationWithRetry(userId, data);
            }
            
            broadcastUserCount();
            broadcastDashboardData();
        }
        
        if (data.type === 'dashboard_connect') {
            isDashboard = true;
            dashboardClients.add(ws);
            console.log('📊 Dashboard connected');
            sendDashboardData(ws);
        }
    });
    
    ws.on('close', () => {
        if (userId) {
            activeUsers.delete(userId);
            broadcastUserCount();
            broadcastDashboardData();
        }
        if (isDashboard) {
            dashboardClients.delete(ws);
        }
    });
});

// ✅ NEW: Enhanced location processing with rate limiting and retry
async function processLocationWithRetry(userId, data, retryCount = 0) {
    try {
        const { timestamp, userAgent, url, locationData } = data;
        const { latitude, longitude, accuracy, altitude, speed } = locationData;
        
        console.log(`🌍 Processing location for ${userId}: ${latitude}, ${longitude}`);
        
        // ✅ Round coordinates to reduce duplicate API calls
        const roundedLat = Math.round(latitude * 100) / 100;
        const roundedLon = Math.round(longitude * 100) / 100;
        const cacheKey = `${roundedLat},${roundedLon}`;
        
        // ✅ Check cache first
        if (locationCache.has(cacheKey)) {
            console.log(`📍 Using cached location for ${userId}`);
            const cachedData = locationCache.get(cacheKey);
            updateUserLocation(userId, cachedData);
            await storeToDatabaseAsync(userId, timestamp, latitude, longitude, cachedData, accuracy, altitude, speed, userAgent, url);
            broadcastDashboardData();
            return;
        }
        
        // ✅ Check if request already pending for this location
        if (pendingRequests.has(cacheKey)) {
            console.log(`⏳ Location request pending for ${userId}, waiting...`);
            
            // Wait for pending request to complete
            try {
                const cachedData = await pendingRequests.get(cacheKey);
                updateUserLocation(userId, cachedData);
                await storeToDatabaseAsync(userId, timestamp, latitude, longitude, cachedData, accuracy, altitude, speed, userAgent, url);
                broadcastDashboardData();
                return;
            } catch (error) {
                console.warn(`⚠️ Pending request failed for ${userId}`);
            }
        }
        
        // ✅ Rate limiting: Wait if needed
        const now = Date.now();
        const timeSinceLastCall = now - lastApiCall;
        if (timeSinceLastCall < API_RATE_LIMIT) {
            const waitTime = API_RATE_LIMIT - timeSinceLastCall;
            console.log(`⏱️ Rate limiting: waiting ${waitTime}ms for ${userId}`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        // ✅ Create promise for this request
        const locationPromise = makeLocationIQRequest(latitude, longitude);
        pendingRequests.set(cacheKey, locationPromise);
        
        try {
            const addressData = await locationPromise;
            
            // ✅ Cache the result
            locationCache.set(cacheKey, addressData);
            
            // ✅ Update user location
            updateUserLocation(userId, addressData);
            
            console.log(`📍 Location resolved for ${userId}: ${addressData.city}, ${addressData.country}`);
            
            // ✅ Store in database
            await storeToDatabaseAsync(userId, timestamp, latitude, longitude, addressData, accuracy, altitude, speed, userAgent, url);
            
            // ✅ Broadcast updates
            broadcastDashboardData();
            
        } catch (error) {
            console.error(`❌ LocationIQ API failed for ${userId}:`, error);
            
            // ✅ Retry logic (max 3 attempts)
            if (retryCount < 3) {
                console.log(`🔄 Retrying location for ${userId} (attempt ${retryCount + 1})`);
                setTimeout(() => {
                    processLocationWithRetry(userId, data, retryCount + 1);
                }, (retryCount + 1) * 2000); // Exponential backoff
            } else {
                console.error(`❌ Max retries reached for ${userId}, keeping as Unknown`);
                // User stays as "Unknown"
            }
        } finally {
            // ✅ Clean up pending request
            pendingRequests.delete(cacheKey);
            lastApiCall = Date.now();
        }
        
    } catch (error) {
        console.error('❌ Location processing error:', error);
    }
}

// ✅ NEW: Separate LocationIQ API call function
async function makeLocationIQRequest(latitude, longitude) {
    const apiUrl = 'https://us1.locationiq.com/v1/reverse.php';
    const params = new URLSearchParams({
        key: LOCATIONIQ_TOKEN,
        lat: latitude,
        lon: longitude,
        format: 'json',
        addressdetails: 1
    });

    const response = await fetch(`${apiUrl}?${params}`);
    
    if (!response.ok) {
        throw new Error(`LocationIQ API error: ${response.status}`);
    }
    
    const locationResponse = await response.json();
    
    return {
        display_name: locationResponse.display_name || 'Unknown location',
        country: locationResponse.address?.country || 'Unknown',
        city: locationResponse.address?.city || 
              locationResponse.address?.town || 
              locationResponse.address?.village || 
              locationResponse.address?.municipality || 
              locationResponse.address?.hamlet || 'Unknown',
        region: locationResponse.address?.region || 
               locationResponse.address?.state || 
               locationResponse.address?.province || 'Unknown',
        state: locationResponse.address?.state || 
              locationResponse.address?.province || 
              locationResponse.address?.region || 'Unknown',
        postcode: locationResponse.address?.postcode || '',
        road: locationResponse.address?.road || 
             locationResponse.address?.street || '',
        suburb: locationResponse.address?.suburb || 
               locationResponse.address?.neighbourhood || 
               locationResponse.address?.quarter || '',
        county: locationResponse.address?.county || '',
        country_code: locationResponse.address?.country_code || ''
    };
}

// ✅ NEW: Helper function to update user location
function updateUserLocation(userId, addressData) {
    const user = activeUsers.get(userId);
    if (user) {
        user.country = addressData.country;
        user.city = addressData.city;
        activeUsers.set(userId, user);
        console.log(`🌍 Updated ${userId} location: ${addressData.city}, ${addressData.country}`);
    }
}

// ✅ Rest of your existing functions (unchanged)
async function storeToDatabaseAsync(userId, timestamp, latitude, longitude, addressData, accuracy, altitude, speed, userAgent, url) {
    let connection;
    try {
        connection = await pool.getConnection();
        await connection.execute(`
            INSERT INTO location_stats (
                user_id, timestamp, latitude, longitude, city, country, region, 
                state, postcode, road, suburb, county, display_name, country_code, accuracy, 
                altitude, speed, user_agent, url
            ) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            userId, timestamp, latitude, longitude,
            addressData.city, addressData.country, addressData.region,
            addressData.state, addressData.postcode, addressData.road,
            addressData.suburb, addressData.county, addressData.display_name, 
            addressData.country_code, accuracy, altitude, speed,
            userAgent, url
        ]);
        
        console.log(`✅ Database updated for ${userId}`);
        
    } catch (error) {
        console.error('❌ Database storage error:', error);
    } finally {
        if (connection) connection.release();
    }
}

function broadcastUserCount() {
    const count = activeUsers.size;
    const message = JSON.stringify({
        type: 'user_count_update',
        count: count
    });
    
    activeUsers.forEach((user) => {
        if (user.ws.readyState === WebSocket.OPEN) {
            user.ws.send(message);
        }
    });
    
    console.log(`📊 ${count} users online`);
}

function getRealTimeUsersByCountry() {
    const countryCount = new Map();
    
    activeUsers.forEach((userData, userId) => {
        const country = userData.country || 'Unknown';
        countryCount.set(country, (countryCount.get(country) || 0) + 1);
    });
    
    const result = Array.from(countryCount.entries()).map(([country, count]) => ({
        country: country,
        user_count: count
    })).sort((a, b) => b.user_count - a.user_count);
    
    return result;
}

async function sendDashboardData(ws) {
    try {
        const usersByCountry = getRealTimeUsersByCountry();
        const totalOnline = activeUsers.size;
        
        const dashboardData = {
            type: 'dashboard_data',
            totalOnline: totalOnline,
            usersByCountry: usersByCountry,
            timestamp: Date.now(),
            dataType: 'real-time'
        };
        
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(dashboardData));
        }
    } catch (error) {
        console.error('❌ Error sending dashboard data:', error);
    }
}

async function broadcastDashboardData() {
    if (dashboardClients.size === 0) return;
    
    try {
        const usersByCountry = getRealTimeUsersByCountry();
        const totalOnline = activeUsers.size;
        
        const dashboardData = {
            type: 'dashboard_data',
            totalOnline: totalOnline,
            usersByCountry: usersByCountry,
            timestamp: Date.now(),
            dataType: 'real-time'
        };
        
        const message = JSON.stringify(dashboardData);
        
        dashboardClients.forEach((client) => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            } else {
                dashboardClients.delete(client);
            }
        });
        
        console.log(`📊 Real-time dashboard data broadcasted to ${dashboardClients.size} clients`);
    } catch (error) {
        console.error('❌ Error broadcasting dashboard data:', error);
    }
}

// ✅ NEW: Cache cleanup (run every 30 minutes)
setInterval(() => {
    locationCache.clear();
    console.log('🗑️ Location cache cleared');
}, 30 * 60 * 1000);

// ✅ Rest of your existing endpoints...
app.get('/api/dashboard', async (req, res) => {
    try {
        const usersByCountry = getRealTimeUsersByCountry();
        const totalOnline = activeUsers.size;
        
        res.json({
            totalOnline: totalOnline,
            usersByCountry: usersByCountry,
            timestamp: Date.now(),
            dataType: 'real-time'
        });
    } catch (error) {
        console.error('❌ Dashboard API error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy',
        online_users: activeUsers.size,
        dashboard_clients: dashboardClients.size,
        location_cache_size: locationCache.size
    });
});

app.get('/dashboard', (req, res) => {
    res.sendFile(__dirname + '/dashboard.html');
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`🔌 WebSocket: ws://localhost:8080`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
});
