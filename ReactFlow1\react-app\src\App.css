/* App.css */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body, html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: #f8fafc;
  overflow: hidden;
}

/* Workflow Builder Container */
.workflow-builder {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* Header */
.workflow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
}

.workflow-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.workflow-status.draft {
  background: #fef3c7;
  color: #92400e;
}

.workflow-status.executing {
  background: #dbeafe;
  color: #1d4ed8;
  animation: pulse 2s infinite;
}

.workflow-status.completed {
  background: #dcfce7;
  color: #166534;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.template-dropdown select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.primary {
  background: #3b82f6;
  color: white;
}

.btn.primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn.secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.btn.secondary:hover {
  background: #e2e8f0;
}

.btn.warning {
  background: #f59e0b;
  color: white;
}

.btn.warning:hover {
  background: #d97706;
}

/* Workflow Canvas */
.workflow-canvas {
  flex: 1;
  position: relative;
}

/* Workflow Node */
.workflow-node {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  width: 280px;
  min-height: 200px;
  position: relative;
  transition: all 0.2s ease;
}

.workflow-node:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.react-flow__node.selected .workflow-node {
  border-color: var(--node-color, #3b82f6);
  box-shadow: 0 0 0 3px var(--node-color, #3b82f6)20;
}

.empty-node {
  border: 2px dashed #cbd5e1;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  cursor: pointer;
}

.empty-node:hover {
  border-color: #94a3b8;
  background: #f1f5f9;
}

.empty-node-content {
  text-align: center;
  color: #64748b;
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #94a3b8;
}

/* Node Header */
.node-header {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
  border-bottom: 1px solid #f1f5f9;
}

.node-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--node-color, #6b7280)15;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-icon {
  font-size: 20px;
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: 600;
  font-size: 16px;
  color: #1e293b;
  margin-bottom: 2px;
}

.node-type {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.workflow-node:hover .node-actions {
  opacity: 1;
}

.node-action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.node-action-btn.configure {
  background: #f1f5f9;
  color: #475569;
}

.node-action-btn.configure:hover {
  background: #e2e8f0;
}

.node-action-btn.delete {
  background: #fef2f2;
  color: #dc2626;
}

.node-action-btn.delete:hover {
  background: #fee2e2;
}

/* Node Content */
.node-content {
  padding: 16px;
}

.node-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
  margin-bottom: 12px;
}

.node-config-preview {
  background: #f8fafc;
  border-radius: 6px;
  padding: 12px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.config-key {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.config-value {
  font-size: 12px;
  font-weight: 600;
}

.config-value.status {
  text-transform: capitalize;
}

/* Node Footer */
.node-footer {
  padding: 16px;
  border-top: 1px solid #f1f5f9;
}

.execute-btn {
  width: 100%;
  padding: 8px 12px;
  background: var(--node-color, #3b82f6)10;
  color: var(--node-color, #3b82f6);
  border: 1px solid var(--node-color, #3b82f6)30;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.execute-btn:hover:not(:disabled) {
  background: var(--node-color, #3b82f6)20;
}

.execute-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Execution Indicator */
.execution-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #6b7280;
  border: 2px solid white;
}

/* Node Handles */
.node-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.target-handle {
  background: #64748b;
  left: -7px;
}

.source-handle {
  background: var(--node-color, #3b82f6);
  right: -7px;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f1f5f9;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Agent Selector Modal */
.agent-selector-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.modal-close:hover {
  background: #f1f5f9;
}

/* Search Section */
.search-section {
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Category Tabs */
.category-tabs {
  display: flex;
  padding: 0 24px;
  border-bottom: 1px solid #e2e8f0;
  overflow-x: auto;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.category-tab:hover {
  color: #1e293b;
}

.category-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.category-count {
  background: #f1f5f9;
  color: #64748b;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.category-tab.active .category-count {
  background: #dbeafe;
  color: #1d4ed8;
}

/* Agents Grid */
.agents-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.agent-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.agent-card:hover {
  border-color: var(--agent-color);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.agent-card-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.agent-card-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.agent-card-content p {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.agent-card-category {
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--agent-color)15;
  color: var(--agent-color);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Configuration Modal */
.configuration-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-icon {
  font-size: 24px;
}

.modal-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
}

.modal-title p {
  font-size: 14px;
  color: #64748b;
}

/* Modal Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
}

.tab {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover {
  color: #1e293b;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

/* Modal Content */
.modal-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.config-input, .config-textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.config-input:focus, .config-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e2e8f0;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .workflow-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-left {
    justify-content: center;
  }
  
  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .agent-selector-modal {
    width: 95vw;
    margin: 20px;
  }
  
  .agents-grid {
    grid-template-columns: 1fr;
  }
  
  .configuration-modal {
    width: 95vw;
    margin: 20px;
  }
}

/* React Flow Customizations */
.react-flow__edge-path {
  stroke: #94a3b8;
  stroke-width: 2;
}

.react-flow__edge.animated path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

.react-flow__controls {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-flow__controls button {
  background: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.react-flow__controls button:hover {
  background: #f8fafc;
}

.react-flow__minimap {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}
