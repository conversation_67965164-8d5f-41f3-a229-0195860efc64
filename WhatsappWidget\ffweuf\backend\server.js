const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');

const app = express();

const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'usertracking',
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
});

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(cors({
    origin: '*',
    methods: ['POST', 'DELETE', 'GET', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

app.options('*', cors());

// FIXED: Enhanced ping endpoint matching your exact database structure
app.post('/ping', async (req, res) => {
    let connection;
    try {
        const { 
            id, 
            timestamp, 
            latitude, 
            longitude, 
            city, 
            country,
            region,
            state,
            postcode,
            road,
            display_name,
            country_code,
            accuracy,
            altitude,
            speed,
            userAgent, 
            url 
        } = req.body;
        
        if (!id || !timestamp) {
            return res.status(400).json({ 
                error: 'Missing required fields: id and timestamp' 
            });
        }

        connection = await pool.getConnection();

        // Convert undefined to null for MySQL
        const onlineUserParams = [
            id,
            timestamp,
            latitude || null,
            longitude || null,
            city || null,
            country || null,
            userAgent || null,
            url || null
        ];

        // Update online_users table
        await connection.execute(`
            INSERT INTO online_users (
                id, timestamp, latitude, longitude, city, country, user_agent, url, updated_at
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
                timestamp = VALUES(timestamp),
                latitude = VALUES(latitude),
                longitude = VALUES(longitude),
                city = VALUES(city),
                country = VALUES(country),
                user_agent = VALUES(user_agent),
                url = VALUES(url),
                updated_at = NOW()
        `, onlineUserParams);

        // FIXED: Store location data matching your exact table structure
        if (latitude && longitude) {
            const locationStatsParams = [
                id,                      // user_id
                timestamp,               // timestamp  
                latitude,               // latitude
                longitude,              // longitude
                city || null,           // city
                country || null,        // country
                region || null,         // region
                state || null,          // state
                postcode || null,       // postcode
                road || null,           // road (add this column first!)
                display_name || null,   // display_name
                country_code || null,   // country_code
                accuracy || null,       // accuracy
                altitude || null,       // altitude
                speed || null,          // speed
                userAgent || null,      // user_agent
                url || null            // url
                // Note: id and created_at are auto-generated
            ];

            // FIXED: Column order matching your database structure
            await connection.execute(`
                INSERT INTO location_stats (
                    user_id, timestamp, latitude, longitude, city, country, region, 
                    state, postcode, road, display_name, country_code, accuracy, 
                    altitude, speed, user_agent, url
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, locationStatsParams);

            console.log(`Location stats stored for user: ${id} in ${city}, ${region}, ${country}`);
        }

        // Clean up expired users
        const expireTime = Date.now() - 60000;
        const [deleteResult] = await connection.execute(
            'DELETE FROM online_users WHERE timestamp < ?', 
            [expireTime]
        );

        // Get current count
        const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM online_users');
        const count = countResult[0].count;

        if (deleteResult.affectedRows > 0) {
            console.log(`Cleaned up ${deleteResult.affectedRows} expired users from online_users`);
        }

        res.json({ 
            count,
            status: 'success',
            timestamp: Date.now(),
            location_stored: !!(latitude && longitude)
        });

    } catch (error) {
        console.error('Database error in /ping:', error);
        res.status(500).json({ 
            error: 'Database error',
            message: error.message 
        });
    } finally {
        if (connection) connection.release();
    }
});

// Rest of your endpoints remain the same...
app.delete('/ping', async (req, res) => {
    let connection;
    try {
        const { id } = req.body;
        
        if (!id) {
            return res.status(400).json({ error: 'Missing user id' });
        }
        
        connection = await pool.getConnection();
        await connection.execute('DELETE FROM online_users WHERE id = ?', [id]);
        const [result] = await connection.execute('SELECT COUNT(*) as count FROM online_users');
        const count = result[0].count;
        
        res.json({ count, status: 'user_removed' });
        
    } catch (error) {
        console.error('Delete error:', error);
        res.status(500).json({ error: 'Database error', message: error.message });
    } finally {
        if (connection) connection.release();
    }
});

app.get('/stats', async (req, res) => {
    let connection;
    try {
        connection = await pool.getConnection();
        
        const [totalResult] = await connection.execute('SELECT COUNT(*) as total FROM online_users');
        const total = totalResult[0].total;

        const [countryResult] = await connection.execute(`
            SELECT country, COUNT(*) as count 
            FROM online_users 
            WHERE country IS NOT NULL AND country != 'Unknown' AND country != ''
            GROUP BY country ORDER BY count DESC LIMIT 10
        `);

        const [cityResult] = await connection.execute(`
            SELECT city, country, COUNT(*) as count 
            FROM online_users 
            WHERE city IS NOT NULL AND city != 'Unknown' AND city != ''
            GROUP BY city, country ORDER BY count DESC LIMIT 10
        `);

        const [recentActivity] = await connection.execute(`
            SELECT COUNT(*) as recent_users FROM online_users 
            WHERE timestamp > ?
        `, [Date.now() - 300000]);

        const [totalLocations] = await connection.execute('SELECT COUNT(*) as total FROM location_stats');
        
        const [locationsByCountry] = await connection.execute(`
            SELECT country, COUNT(*) as count FROM location_stats 
            WHERE country IS NOT NULL AND country != 'Unknown' AND country != ''
            GROUP BY country ORDER BY count DESC LIMIT 10
        `);

        const [locationsByCity] = await connection.execute(`
            SELECT city, region, country, COUNT(*) as count FROM location_stats 
            WHERE city IS NOT NULL AND city != 'Unknown' AND city != ''
            GROUP BY city, region, country ORDER BY count DESC LIMIT 10
        `);

        const [recentLocations] = await connection.execute(`
            SELECT city, country, region, COUNT(*) as count FROM location_stats 
            WHERE timestamp > ? GROUP BY city, country, region
            ORDER BY count DESC LIMIT 5
        `, [Date.now() - 86400000]);

        res.json({
            online_users: {
                total,
                recent_activity: recentActivity[0].recent_users,
                by_country: countryResult,
                top_cities: cityResult
            },
            location_stats: {
                total_locations: totalLocations[0].total,
                by_country: locationsByCountry,
                top_cities: locationsByCity,
                recent_24h: recentLocations
            },
            timestamp: Date.now()
        });

    } catch (error) {
        console.error('Stats error:', error);
        res.status(500).json({ error: 'Database error', message: error.message });
    } finally {
        if (connection) connection.release();
    }
});

app.get('/location-stats', async (req, res) => {
    let connection;
    try {
        connection = await pool.getConnection();
        
        const [totalLocations] = await connection.execute('SELECT COUNT(*) as total FROM location_stats');
        
        const [countryStats] = await connection.execute(`
            SELECT country, COUNT(*) as total_visits, COUNT(DISTINCT user_id) as unique_users,
                   MIN(FROM_UNIXTIME(timestamp/1000)) as first_visit,
                   MAX(FROM_UNIXTIME(timestamp/1000)) as last_visit
            FROM location_stats 
            WHERE country IS NOT NULL AND country != 'Unknown' AND country != ''
            GROUP BY country ORDER BY total_visits DESC
        `);

        const [cityStats] = await connection.execute(`
            SELECT city, region, country, COUNT(*) as total_visits,
                   COUNT(DISTINCT user_id) as unique_users, AVG(accuracy) as avg_accuracy
            FROM location_stats 
            WHERE city IS NOT NULL AND city != 'Unknown' AND city != ''
            GROUP BY city, region, country ORDER BY total_visits DESC LIMIT 20
        `);

        const [timeStats] = await connection.execute(`
            SELECT DATE(FROM_UNIXTIME(timestamp/1000)) as date,
                   COUNT(*) as visits, COUNT(DISTINCT user_id) as unique_users
            FROM location_stats WHERE timestamp > ?
            GROUP BY DATE(FROM_UNIXTIME(timestamp/1000))
            ORDER BY date DESC LIMIT 30
        `, [Date.now() - (30 * 24 * 60 * 60 * 1000)]);

        res.json({
            total_locations: totalLocations[0].total,
            country_stats: countryStats,
            city_stats: cityStats,
            daily_stats: timeStats,
            timestamp: Date.now()
        });

    } catch (error) {
        console.error('Location stats error:', error);
        res.status(500).json({ error: 'Database error', message: error.message });
    } finally {
        if (connection) connection.release();
    }
});

app.get('/health', async (req, res) => {
    let connection;
    try {
        connection = await pool.getConnection();
        const [onlineResult] = await connection.execute('SELECT COUNT(*) as count FROM online_users');
        const [locationResult] = await connection.execute('SELECT COUNT(*) as count FROM location_stats');
        
        res.json({ 
            status: 'healthy', 
            database: 'connected',
            online_users: onlineResult[0].count,
            total_location_records: locationResult[0].count,
            timestamp: Date.now() 
        });
    } catch (error) {
        console.error('Health check failed:', error);
        res.status(500).json({ 
            status: 'unhealthy', 
            database: 'disconnected',
            error: error.message,
            timestamp: Date.now()
        });
    } finally {
        if (connection) connection.release();
    }
});

// Cleanup job
const cleanupInterval = setInterval(async () => {
    let connection;
    try {
        connection = await pool.getConnection();
        const expireTime = Date.now() - 60000;
        const [result] = await connection.execute(
            'DELETE FROM online_users WHERE timestamp < ?', [expireTime]
        );
        
        if (result.affectedRows > 0) {
            console.log(`Background cleanup: Removed ${result.affectedRows} expired users`);
        }
    } catch (error) {
        console.error('Background cleanup error:', error);
    } finally {
        if (connection) connection.release();
    }
}, 30000);

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    clearInterval(cleanupInterval);
    pool.end().then(() => {
        console.log('Database pool closed');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    clearInterval(cleanupInterval);
    pool.end().then(() => {
        console.log('Database pool closed');
        process.exit(0);
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Microserver running on port ${PORT}`);
    console.log(`Health check available at: http://localhost:${PORT}/health`);
    console.log(`Stats available at: http://localhost:${PORT}/stats`);
    console.log(`Location stats available at: http://localhost:${PORT}/location-stats`);
});
