try {
  document.addEventListener("DOMContentLoaded", async function () {
    const p = document.getElementById("authkey-chat-widget");

    if (p) {
      const originalText = "Hello, world!";
      const encodedText = encodeURIComponent(originalText);

      const ch = p.getAttribute("widget-id");

      const d = await fetch(
        `https://napi.authkey.io/api/whatsapp_widget?method=retrieve_wg_token&wig_token=${ch}`
      );
      const nd = await d.json();
      const ndn = nd.success === true ? nd.data[0] : null;
      const pe2 = document.getElementById(ch);
      
      function ft() {
        const now = new Date();
        let hrs = now.getHours();
        const mnt = now.getMinutes();
        const ampm = hrs >= 12 ? "PM" : "AM";
        hrs = hrs % 12;
        hrs = hrs ? hrs : 12;
        const mf = mnt.toString().padStart(2, "0");
        const tf = `${hrs}:${mf} ${ampm}`;
        return tf;
      }

      // UPDATED: Online User Tracker with all location fields
      class OnlineUserTracker {
        constructor() {
          this.userId = this.generateUserId();
          this.serverUrl = 'http://localhost:3000/ping'; 
          this.heartbeatInterval = 30000; // 30 seconds
        }

        generateUserId() {
          return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
        }

        async updateUserStatus(locationData = null) {
          try {
            const payload = {
              id: this.userId,
              timestamp: Date.now(),
              url: window.location.href,
              userAgent: navigator.userAgent,
              ...(locationData && {
                latitude: locationData.latitude,
                longitude: locationData.longitude,
                city: locationData.address?.city,
                country: locationData.address?.country,
                region: locationData.address?.region,
                state: locationData.address?.state,
                county: locationData.address?.county,
                postcode: locationData.address?.postcode,
                road: locationData.address?.road,
                house_number: locationData.address?.house_number,
                suburb: locationData.address?.suburb,
                display_name: locationData.address?.display_name,
                country_code: locationData.address?.country_code,
                place_id: locationData.address?.place_id,
                osm_type: locationData.address?.osm_type,
                osm_id: locationData.address?.osm_id,
                accuracy: locationData.accuracy,
                altitude: locationData.altitude,
                speed: locationData.speed
              })
            };

            const response = await fetch(this.serverUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(payload)
            });

            if (response.ok) {
              const data = await response.json();
              this.updateDisplay(data.count);
              
              if (data.location_stored) {
                console.log('Complete location data stored in stats table');
              }
              
              return data.count;
            }
          } catch (error) {
            console.warn('Server ping failed:', error);
          }
          return 1; // Fallback count
        }

        updateDisplay(count) {
          const onlineElement = document.querySelector('.authkey-whatsappOnline');
          if (onlineElement) {
            onlineElement.textContent = count > 1 ? `${count} users online` : 'Online';
          }
        }

        startHeartbeat() {
          // Initial ping
          this.updateUserStatus();
          
          // Regular heartbeat
          setInterval(() => {
            this.updateUserStatus();
          }, this.heartbeatInterval);

          // Cleanup on page unload
          window.addEventListener('beforeunload', () => {
            this.setUserOffline();
          });
        }

        async setUserOffline() {
          try {
            await fetch(this.serverUrl, {
              method: 'DELETE',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ id: this.userId })
            });
          } catch (error) {
            // Fail silently
          }
        }
      }

      // UPDATED: Enhanced Geolocation with proper LocationIQ mapping and permission flow
      class WhatsAppGeolocation {
        constructor(phoneNumber) {
          this.locationIQToken = '***********************************';
          this.phoneNumber = phoneNumber;
        }

        getCurrentLocation(successCallback, errorCallback) {
          if (!navigator.geolocation) {
            errorCallback(new Error('Geolocation not supported'));
            return;
          }

          const options = {
            enableHighAccuracy: true,
            timeout: 8000,
            maximumAge: 300000 // 5 minutes cache
          };

          navigator.geolocation.getCurrentPosition(
            (position) => {
              const location = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                altitude: position.coords.altitude,
                speed: position.coords.speed,
                timestamp: Date.now()
              };
              successCallback(location);
            },
            errorCallback,
            options
          );
        }

        async reverseGeocode(latitude, longitude) {
          try {
            const url = `https://us1.locationiq.com/v1/reverse.php`;
            const params = new URLSearchParams({
              key: this.locationIQToken,
              lat: latitude,
              lon: longitude,
              format: 'json',
              addressdetails: 1
            });

            const response = await fetch(`${url}?${params}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            
            const data = await response.json();
            
            // FIXED: Proper LocationIQ response mapping based on their docs
            return {
              display_name: data.display_name || 'Unknown location',
              country: data.address?.country || 'Unknown country',
              country_code: data.address?.country_code || '',
              
              // LocationIQ uses different fields for region/state - proper mapping
              region: data.address?.region || data.address?.state || data.address?.province || 'Unknown region',
              state: data.address?.state || data.address?.province || data.address?.region || 'Unknown state',
              
              // Multiple options for city
              city: data.address?.city || 
                    data.address?.town || 
                    data.address?.village || 
                    data.address?.municipality || 
                    data.address?.hamlet || 'Unknown city',
                    
              // Additional LocationIQ fields
              county: data.address?.county || '',
              postcode: data.address?.postcode || '',
              road: data.address?.road || data.address?.street || '',
              house_number: data.address?.house_number || '',
              suburb: data.address?.suburb || data.address?.neighbourhood || data.address?.quarter || '',
              
              // LocationIQ specific fields
              place_id: data.place_id || '',
              osm_type: data.osm_type || '',
              osm_id: data.osm_id || '',
              licence: data.licence || ''
            };
          } catch (error) {
            console.error('LocationIQ reverse geocoding error:', error);
            return {
              country: 'Unknown',
              region: 'Unknown',
              city: 'Unknown',
              display_name: 'Location unavailable'
            };
          }
        }

        // FIXED: Wait for location permission BEFORE opening WhatsApp
        async handleSendClick() {
          console.log('Send button clicked - requesting location first...');
          
          // Show loading state
          const sendButton = document.getElementById('send-button');
          const originalText = sendButton.innerHTML;
          sendButton.innerHTML = '<span style="font-size: 14px; color: white;">📍</span>';
          sendButton.disabled = true;

          // FIXED: Wait for location permission first
          this.getCurrentLocation(async (location) => {
            try {
              console.log('Location permission granted, processing...');
              
              // Get reverse geocoded address with all details
              const addressData = await this.reverseGeocode(location.latitude, location.longitude);
              const completeLocationData = { ...location, address: addressData };
              
              console.log('Complete location data with region:', completeLocationData);
              console.log('Region extracted:', addressData.region);
              console.log('State extracted:', addressData.state);
              console.log('City extracted:', addressData.city);
              console.log('Country extracted:', addressData.country);
              
              // Update global user count with ALL location details
              if (window.userTracker) {
                await window.userTracker.updateUserStatus(completeLocationData);
              }
              
              // Store locally for reference
              localStorage.setItem('authkey_last_location', JSON.stringify(completeLocationData));
              
              // Restore button and open WhatsApp
              sendButton.innerHTML = originalText;
              sendButton.disabled = false;
              this.openWhatsApp();
              
            } catch (error) {
              console.warn('Location processing failed:', error);
              
              // Restore button and open WhatsApp anyway
              sendButton.innerHTML = originalText;
              sendButton.disabled = false;
              this.openWhatsApp();
            }
          }, async (error) => {
            console.warn('Location access denied:', error);
            
            // Update count without location
            if (window.userTracker) {
              await window.userTracker.updateUserStatus(null);
            }
            
            // Restore button and open WhatsApp
            sendButton.innerHTML = originalText;
            sendButton.disabled = false;
            this.openWhatsApp();
          });
        }

        openWhatsApp() {
          console.log('Opening WhatsApp...');
          
          // Clear message input
          const messageInput = document.getElementById('message-input');
          if (messageInput) {
            messageInput.value = '';
          }
          
          // Open WhatsApp without any pre-filled content
          const url = `https://wa.me/${this.phoneNumber}`;
          window.open(url, "_blank");
        }

        attachToSendButton() {
          const sendButton = document.getElementById('send-button');
          if (sendButton && !sendButton.hasAttribute('data-geo-attached')) {
            sendButton.setAttribute('data-geo-attached', 'true');
            sendButton.addEventListener('click', () => {
              this.handleSendClick();
            });
          }
        }
      }

      var nwc = `
             <style>
            .authkey-dh{
            display:none;
            
            opacity:0;
            marigin-bottom:0;
            }
            .authkey-ds{
            display:block;
            opactiy:1;
            margin-bottom:20px;
            }
        .authkey-whatsappBtn {
            border-radius: 100%;
            line-height: 1.32;
            color: rgb(255, 255, 255);
            font-size: 0px;
            background-color: #25d366;
            border-width: 0px;
            padding: 0px;
            height: fit-content;
            width: fit-content;
            cursor: pointer;
            position: relative;
            float:${ndn.wig_position};
        }
        .authkey-whatsappIcon{
            fill: white;
            width: 3.125rem;
            height: 3.125rem;
            padding: 0.4375rem;
        }
        .authkey-whatsappLive {
            background-color: rgb(255, 0, 0);
            position: absolute;
            z-index: 1;
            border-radius: 50%;
            display: block !important;
            height: .6rem;
            width: .6rem;
            font-size: .687rem;
            top: 7px;
            right: 2px;
        }
        .authkey-whatsappHeader {
            color: rgb(17, 17, 17);
            display: flex;
            -webkit-box-align: center;
            align-items: center;
            padding: 18px;
            background: ${ndn.back_color};
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .authkey-whatsappAvataarContainer {
            position: relative;
            width: 52px;
            height: 52px;
            box-shadow: rgba(17, 17, 17, 0.1) 0px 0px 2px inset;
            border-radius: 50%;
            display: block;
            flex-shrink: 0;
            overflow: inherit;
            cursor: pointer;
        }
        .authkey-whatsappAvataar {
            width: 52px;
            height: 52px;
            background-color: rgb(210, 210, 210);
            opacity: 1;
            border-radius: 50%;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }
       
        .authkey-whatsappAvataarImg {
            object-fit: cover;
            display: inline-block !important;
            position: static !important;
            margin: 0px !important;
            padding: 0px !important;
            max-width: none !important;
            height: inherit !important;
            width: inherit !important;
            visibility: visible !important;
        }
        .authkey-whatsappAvataarContainer:before {
            content: "";
            bottom: 0px;
            right: 0px;
            width: 12px;
            height: 12px;
            box-sizing: border-box;
            position: absolute;
            z-index: 2;
            border-radius: 50%;
            background-color: rgb(74, 213, 4);
            display: block;
            border: 2px solid rgb(0, 128, 105);
        }
        .authkey-whatsappClientImg:before {
            content: "";
            bottom: 0px;
            right: 0px;
            width: 12px;
            height: 12px;
            box-sizing: border-box;
            position: absolute;
            z-index: 2;
            border-radius: 50%;
            background-color: rgb(74, 213, 4);
            display: block;
            border: 2px solid rgb(0, 128, 105);
        }
        .authkey-whatsappWindow {
        
            z-index: 2147483647;
            width: 300px;
            pointer-events: all;
            touch-action: auto;
            
            transition: opacity 0.3s, margin 0.3s, visibility 0.3s;
        
            inset: auto 20px 76px auto;
        }
        .authkey-whatsappWindowShadow {
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 30px 0px;
        }
        .authkey-whatsappCloseIcon {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            opacity: 0.4;
            cursor: pointer;
            transition: 0.3s;
            outline: transparent;
            -webkit-box-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            align-items: center;
            display: flex !important;
        }
        .authkey-whatsappCloseIcon:before, .authkey-whatsappCloseIcon:after {
            content: "";
            position: absolute;
            width: 12px;
            height: 2px;
            background-color: rgb(255, 255, 255);
            display: block;
            border-radius: 2px;
        }
        .authkey-whatsappCloseIcon:before {
            transform: rotate(45deg);
        }
        .authkey-whatsappCloseIcon:after {
            transform: rotate(-45deg);
        }
        .authkey-whatsappHeaderInfo {
            margin-left: 16px;
            margin-right: 16px;
            width: 100%;
            overflow: hidden;
        }
        .authkey-whatsappAvataarName {
            font-size: 16px;
            font-weight: 700;
            line-height: 20px;
            max-height: 60px;
            -webkit-line-clamp: 3;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            color: rgb(255, 255, 255);
            font-family:Arial, Helvetica, sans-serif;
        }
        .authkey-whatsappOnline {
            font-size: 13px;
            line-height: 18px;
            margin-top: 4px;
            color: rgb(255, 255, 255);
            font-family:Arial, Helvetica, sans-serif;
        }
        .authkey-whatsappBottomLayout {
            background: url(https://static.elfsight.com/apps/all-in-one-chat/patterns/background-whatsapp.jpg) center center / cover no-repeat;
        }
        .authkey-whatsappChatbox {
            position: relative;
            padding: 20px 20px 12px;
            overflow: auto;
            max-height: 382px;
        }
        .authkey-whatsappChatLayout {
            padding: 6px 14px;
            position: relative;
            transform-origin: center top;
            z-index: 2;
            color: rgb(255, 255, 255);
            font-size: 15px;
            line-height: 1.39;
            max-width: calc(100% - 50px);
            border-radius: 0px 16px 16px;
            background-color: rgb(255, 255, 255);
            opacity: 1;
            hyphens: auto;
            box-shadow: rgba(0, 0, 0, 0.15) 0px 1px 0px 0px;
        }
        .authkey-whatsappChatMessage {
            display: flex;
            align-items: flex-end;
            color: #000;
            font-family:Arial, Helvetica, sans-serif;
        }
        .authkey-whatsappChatSvg {
            position: absolute;
            top: 0px;
            left: -9px;
        }
        .authkey-whatsappChatTime {
            text-align: right;
            margin-left: 12px;
            font-size: 12px;
            line-height: 14px;
            opacity: 0.5;
            color: #000;
            font-family:Arial, Helvetica, sans-serif;
        }
        .authkey-whatsappBtnInline {
            border-radius: 24px;
            border-color: rgba(255, 255, 255, 0.1);
            width: auto;
            line-height: 1.32;
            color: rgb(255, 255, 255);
            font-family: inherit;
            font-weight: bold;
            font-size: 16px;
            background-color: rgb(37, 211, 102);
            border-width: 0px;
            padding: 0px;
            margin: 20px;
            max-width: 100%;
            box-shadow: rgba(0, 0, 0, 0.25) 0px 1px 0px 0px;
            padding: 12px 27px;
            cursor:pointer;
        }
        .authkey-whatsappBottomnext {
            display: flex;
            -webkit-box-pack: center;
            justify-content: center;
        }
        .authkey-m-d {
            position: fixed;
            bottom: ${ndn.bottom_align}px;
            ${ndn.wig_position}: ${ndn.side_align}px;
            Z-index:10000;
        }
            .authkey-branding {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  margin: 0;
  p{
    font-size: 10px;
    margin-top: 4px;
    margin-bottom: 4px;
    margin-left: 1%;
    margin-right: 1%;
  }
  img{
    width: 0.8em;
  }
  a{
    font-size: 10px;
    text-decoration: none;
  }
}

    </style>
    <div class="authkey-m-d">
    <div id="authkey-tgl" class="authkey-whatsappWindow ">
        <div class="authkey-whatsappWindowShadow">
            <div role="button" id="authkey-cbtn" tabindex="0" class="authkey-whatsappCloseIcon"></div>
            <div class="authkey-whatsappHeader">
                <div class="authkey-whatsappAvataarContainer">
                    <div class="authkey-whatsappAvataar">
                        <img src=${
                          ndn.image_url
                        } alt="user_image" class="authkey-whatsappAvataarImg">
                    </div>
                </div>
                <div class="authkey-whatsappHeaderInfo">
                    <div class="authkey-whatsappAvataarName">${
                      ndn.display_name
                    }</div>
                    <div class="authkey-whatsappOnline">Online</div>
                </div>
            </div>
            <div class="authkey-whatsappBottomLayout">
                <div class="authkey-whatsappChatbox">
                    <div class="authkey-whatsappChatLayout">
                        <svg xmlns="http://www.w3.org/2000/svg" width="9" height="17" viewBox="0 0 9 17" fill="currentColor" class="authkey-whatsappChatSvg"><path d="M0.772965 3.01404C-0.0113096 1.68077 0.950002 0 2.49683 0H9V17L0.772965 3.01404Z" fill="currentColor"></path></svg>
                        <div class="authkey-whatsappChatMessage">
                            <div>${ndn.welcome_text}</div>
                        </div>
                        <div class="authkey-whatsappChatTime">
                            ${ft()}
                        </div>
                    </div>
                    <div style="display: flex-direction: column;  position: relative; background-color: transparent;">
 <div style="display: flex; margin-top: 30px;  flex-direction: column; position: relative; background-color: transparent; overflow-x: hidden;">
  <!-- Message Input -->
  <textarea
    id="message-input"
    placeholder="Type your text here..."
    style="width: 100%; height: 30px; padding: 12px; border: 1px solid #e1e1e1; font-size: 15px; background-color: #f7f7f7; resize: none; outline: none; color: #333; box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; padding-right: 50px; border-radius: 15px;" 
  ></textarea>

  <!-- Send Button -->
  <button
    id="send-button"
    style="background-color: #25d366; border: none; border-radius: 50%; width: 45px; height: 45px; display: flex; justify-content: center; align-items: center; cursor: pointer; position: absolute; right: 0; top: 50%; transform: translateY(-50%); transition: background-color 0.3s ease;"
  >
    <span
      style="font-size: 20px; color: white;  font-weight: bold; line-height: 0;"
    >➤</span>
  </button>
</div>

</div>

                </div>
            </div>
              <div
      id="whatsapp-container"
      style="width: 100%; background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); padding: 16px; display: flex; flex-direction: column; font-family: 'Roboto', sans-serif;"
    >
     
    </div>
            <div class="authkey-branding">
              <span style="font-size: 0.8em;">⚡</span>
              <p>by   </p>
              <a href="https://authkey.io" target="_blank" >
                Authkey.io
              </a>
            </div>
            
        </div>
        
    </div>

    <button class="authkey-whatsappBtn" id="authkey-wt-btn">
        <svg viewBox="0 0 32 32" class="authkey-whatsappIcon"><path d=" M19.11 17.205c-.372 0-1.088 1.39-1.518 1.39a.63.63 0 0 1-.315-.1c-.802-.402-1.504-.817-2.163-1.447-.545-.516-1.146-1.29-1.46-1.963a.426.426 0 0 1-.073-.215c0-.33.99-.945.99-1.49 0-.143-.73-2.09-.832-2.335-.143-.372-.214-.487-.6-.487-.187 0-.36-.043-.53-.043-.302 0-.53.115-.746.315-.688.645-1.032 1.318-1.06 2.264v.114c-.015.99.472 1.977 1.017 2.78 1.23 1.82 2.506 3.41 4.554 4.34.616.287 2.035.888 2.722.888.817 0 2.15-.515 2.478-1.318.13-.33.244-.73.244-1.088 0-.058 0-.144-.03-.215-.1-.172-2.434-1.39-2.678-1.39zm-2.908 7.593c-1.747 0-3.48-.53-4.942-1.49L7.793 24.41l1.132-3.337a8.955 8.955 0 0 1-1.72-5.272c0-4.955 4.04-8.995 8.997-8.995S25.2 10.845 25.2 15.8c0 4.958-4.04 8.998-8.998 8.998zm0-19.798c-5.96 0-10.8 4.842-10.8 10.8 0 1.964.53 3.898 1.546 5.574L5 27.176l5.974-1.92a10.807 10.807 0 0 0 16.03-9.455c0-5.958-4.842-10.8-10.802-10.8z" fill-rule="evenodd"></path></svg>
        <span class="authkey-whatsappLive"></span>
    </button>
    </div>
`;
      const cw = document.createElement("div");
      cw.id = "authkey-wp-widget-container";
      document.body.appendChild(cw);

      cw.innerHTML += nwc;

      // Initialize tracking systems
      window.userTracker = new OnlineUserTracker();
      window.whatsappGeo = new WhatsAppGeolocation(ndn.mobile_no);

      const wtbtn = document.getElementById("authkey-wt-btn");
      const td = document.getElementById("authkey-tgl");
      const cbtn = document.getElementById("authkey-cbtn");
      const wprb = document.getElementById("authkey-wp-r-b");
      
      if (ndn.default_open === 1) {
        td.classList.add("authkey-dh");

        setTimeout(() => {
          td.classList.remove("authkey-dh");
          td.classList.add("authkey-ds");
         
        }, 3000);
      } else {
        td.classList.add("authkey-dh");
      }
      
      // Fixed: Check if wprb exists before adding event listener
      if (wprb) {
        wprb.addEventListener("click", () => {
          const url = `https://wa.me/${ndn.mobile_no}`;
          window.open(url, "_blank");
        });
      }
      
      cbtn.addEventListener("click", () => {
        if (td.classList.contains("authkey-ds")) {
          td.classList.remove("authkey-ds");
          td.classList.add("authkey-dh");
        }
      });
      
      wtbtn.addEventListener("click", () => {
        if (td.classList.contains("authkey-ds")) {
          td.classList.remove("authkey-ds");
          td.classList.add("authkey-dh");
        } else {
          td.classList.remove("authkey-dh");
          td.classList.add("authkey-ds");
        }
       
      });

      // Attach geolocation to send button
      setTimeout(() => {
        window.whatsappGeo.attachToSendButton();
      }, 500);

      // Start heartbeat for user tracking
      setTimeout(() => {
        window.userTracker.startHeartbeat();
      }, 1000);

    } else {
      console.log("Parent element not found");
    }
  });
} catch (error) {
  console.log(error);
}
