import React, { useCallback, useState, useEffect } from 'react';
import {
  ReactFlow,
  addEdge,
  Background,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Handle,
  Position,
  ConnectionLineType,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import './App.css';

// Agent Templates with different categories
const AGENT_TEMPLATES = {
  triggers: [
    {
      id: 'webhook',
      name: 'Webhook',
      description: 'Receive HTTP requests',
      icon: '🎣',
      color: '#ff6d5a',
      category: 'trigger',
      config: {
        url: '',
        method: 'POST',
        authentication: false
      }
    },
    {
      id: 'schedule',
      name: 'Schedule',
      description: 'Run on a schedule',
      icon: '⏰',
      color: '#ff6d5a',
      category: 'trigger',
      config: {
        cron: '0 9 * * 1-5',
        timezone: 'UTC'
      }
    },
    {
      id: 'email_trigger',
      name: '<PERSON><PERSON> Received',
      description: 'Trigger when email is received',
      icon: '📧',
      color: '#ff6d5a',
      category: 'trigger',
      config: {
        provider: 'gmail',
        folder: 'inbox',
        filters: []
      }
    }
  ],
  actions: [
    {
      id: 'send_email',
      name: 'Send Email',
      description: 'Send emails via SMTP',
      icon: '📨',
      color: '#3b82f6',
      category: 'action',
      config: {
        to: '',
        subject: '',
        body: '',
        provider: 'smtp'
      }
    },
    {
      id: 'http_request',
      name: 'HTTP Request',
      description: 'Make HTTP API calls',
      icon: '🌐',
      color: '#3b82f6',
      category: 'action',
      config: {
        url: '',
        method: 'GET',
        headers: {},
        body: ''
      }
    },
    {
      id: 'database',
      name: 'Database',
      description: 'Query or update database',
      icon: '🗄️',
      color: '#3b82f6',
      category: 'action',
      config: {
        operation: 'select',
        table: '',
        query: ''
      }
    },
    {
      id: 'slack',
      name: 'Slack',
      description: 'Send Slack messages',
      icon: '💬',
      color: '#3b82f6',
      category: 'action',
      config: {
        channel: '',
        message: '',
        webhook_url: ''
      }
    },
    {
      id: 'file_operations',
      name: 'File Operations',
      description: 'Read, write, or process files',
      icon: '📁',
      color: '#3b82f6',
      category: 'action',
      config: {
        operation: 'read',
        path: '',
        format: 'json'
      }
    }
  ],
  conditions: [
    {
      id: 'if_condition',
      name: 'IF Condition',
      description: 'Branch based on conditions',
      icon: '🔀',
      color: '#f59e0b',
      category: 'condition',
      config: {
        field: '',
        operator: 'equals',
        value: ''
      }
    },
    {
      id: 'filter',
      name: 'Filter',
      description: 'Filter data based on criteria',
      icon: '🔍',
      color: '#f59e0b',
      category: 'condition',
      config: {
        conditions: []
      }
    }
  ],
  transformers: [
    {
      id: 'data_transform',
      name: 'Transform Data',
      description: 'Transform and map data',
      icon: '🔧',
      color: '#10b981',
      category: 'transformer',
      config: {
        mappings: {},
        operations: []
      }
    },
    {
      id: 'json_parser',
      name: 'JSON Parser',
      description: 'Parse and extract JSON data',
      icon: '📋',
      color: '#10b981',
      category: 'transformer',
      config: {
        path: '',
        output_format: 'object'
      }
    }
  ],
  ai_agents: [
    {
      id: 'openai_gpt',
      name: 'OpenAI GPT',
      description: 'Generate text with GPT models',
      icon: '🤖',
      color: '#8b5cf6',
      category: 'ai',
      config: {
        model: 'gpt-3.5-turbo',
        prompt: '',
        max_tokens: 150,
        temperature: 0.7
      }
    },
    {
      id: 'image_recognition',
      name: 'Image Recognition',
      description: 'Analyze images with AI',
      icon: '👁️',
      color: '#8b5cf6',
      category: 'ai',
      config: {
        service: 'openai',
        analysis_type: 'description'
      }
    }
  ]
};

// Configuration Modal Component
function ConfigurationModal({ node, isOpen, onClose, onSave }) {
  const [config, setConfig] = useState(node?.data?.config || {});
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (node?.data?.config) {
      setConfig(node.data.config);
    }
  }, [node]);

  if (!isOpen || !node) return null;

  const handleConfigChange = (key, value) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = () => {
    onSave(node.id, config);
    onClose();
  };

  const renderConfigFields = () => {
    const agent = node.data.agent;
    if (!agent || !agent.config) return null;

    return Object.entries(agent.config).map(([key, defaultValue]) => (
      <div key={key} className="config-field">
        <label className="config-label">
          {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </label>
        {typeof defaultValue === 'boolean' ? (
          <input
            type="checkbox"
            checked={config[key] || false}
            onChange={(e) => handleConfigChange(key, e.target.checked)}
            className="config-checkbox"
          />
        ) : typeof defaultValue === 'object' ? (
          <textarea
            value={JSON.stringify(config[key] || defaultValue, null, 2)}
            onChange={(e) => {
              try {
                handleConfigChange(key, JSON.parse(e.target.value));
              } catch (err) {
                // Handle invalid JSON
              }
            }}
            className="config-textarea"
            rows="3"
          />
        ) : (
          <input
            type="text"
            value={config[key] || ''}
            onChange={(e) => handleConfigChange(key, e.target.value)}
            className="config-input"
            placeholder={`Enter ${key.replace(/_/g, ' ')}`}
          />
        )}
      </div>
    ));
  };

  return (
    <div className="modal-overlay">
      <div className="configuration-modal">
        <div className="modal-header">
          <div className="modal-title">
            <span className="agent-icon">{node.data.agent?.icon}</span>
            <div>
              <h3>{node.data.agent?.name} Configuration</h3>
              <p>{node.data.agent?.description}</p>
            </div>
          </div>
          <button onClick={onClose} className="modal-close">×</button>
        </div>

        <div className="modal-tabs">
          <button 
            className={`tab ${activeTab === 'basic' ? 'active' : ''}`}
            onClick={() => setActiveTab('basic')}
          >
            Basic Settings
          </button>
          <button 
            className={`tab ${activeTab === 'advanced' ? 'active' : ''}`}
            onClick={() => setActiveTab('advanced')}
          >
            Advanced
          </button>
        </div>

        <div className="modal-content">
          {activeTab === 'basic' && (
            <div className="config-section">
              <div className="config-field">
                <label className="config-label">Node Name</label>
                <input
                  type="text"
                  value={config.name || node.data.label}
                  onChange={(e) => handleConfigChange('name', e.target.value)}
                  className="config-input"
                />
              </div>
              {renderConfigFields()}
            </div>
          )}
          
          {activeTab === 'advanced' && (
            <div className="config-section">
              <div className="config-field">
                <label className="config-label">Retry on Failure</label>
                <input
                  type="checkbox"
                  checked={config.retryOnFailure || false}
                  onChange={(e) => handleConfigChange('retryOnFailure', e.target.checked)}
                  className="config-checkbox"
                />
              </div>
              <div className="config-field">
                <label className="config-label">Timeout (seconds)</label>
                <input
                  type="number"
                  value={config.timeout || 30}
                  onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
                  className="config-input"
                />
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button onClick={onClose} className="btn secondary">Cancel</button>
          <button onClick={handleSave} className="btn primary">Save Configuration</button>
        </div>
      </div>
    </div>
  );
}

// Agent Selector Modal
function AgentSelector({ isOpen, onClose, onSelectAgent, position }) {
  const [selectedCategory, setSelectedCategory] = useState('triggers');
  const [searchTerm, setSearchTerm] = useState('');

  if (!isOpen) return null;

  const filteredAgents = AGENT_TEMPLATES[selectedCategory]?.filter(agent =>
    agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.description.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleSelectAgent = (agent) => {
    onSelectAgent(agent, position);
    onClose();
    setSearchTerm('');
  };

  return (
    <div className="modal-overlay">
      <div className="agent-selector-modal">
        <div className="modal-header">
          <h3>Select an Agent</h3>
          <button onClick={onClose} className="modal-close">×</button>
        </div>

        <div className="search-section">
          <input
            type="text"
            placeholder="Search agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="category-tabs">
          {Object.keys(AGENT_TEMPLATES).map(category => (
            <button
              key={category}
              className={`category-tab ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
              <span className="category-count">({AGENT_TEMPLATES[category].length})</span>
            </button>
          ))}
        </div>

        <div className="agents-grid">
          {filteredAgents.map(agent => (
            <div
              key={agent.id}
              className="agent-card"
              onClick={() => handleSelectAgent(agent)}
              style={{ '--agent-color': agent.color }}
            >
              <div className="agent-card-icon">{agent.icon}</div>
              <div className="agent-card-content">
                <h4>{agent.name}</h4>
                <p>{agent.description}</p>
              </div>
              <div className="agent-card-category">{agent.category}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Enhanced Node Component
function WorkflowNode({ data, id, selected }) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState(data.status || 'idle');

  const handleExecute = async () => {
    setIsExecuting(true);
    setExecutionStatus('executing');
    
    // Simulate execution
    setTimeout(() => {
      setIsExecuting(false);
      setExecutionStatus('success');
      if (data.onStatusChange) {
        data.onStatusChange(id, 'success');
      }
    }, 2000);
  };

  const getNodeStyle = () => {
    const agent = data.agent;
    if (!agent) return {};
    
    return {
      '--node-color': agent.color,
      '--node-bg': selected ? `${agent.color}20` : 'white'
    };
  };

  const getExecutionStatusColor = () => {
    switch (executionStatus) {
      case 'executing': return '#3b82f6';
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (!data.agent) {
    return (
      <div className="workflow-node empty-node">
        <div className="empty-node-content">
          <div className="empty-icon">+</div>
          <p>Select an agent</p>
        </div>
        <Handle type="target" position={Position.Left} className="node-handle" />
        <Handle type="source" position={Position.Right} className="node-handle" />
      </div>
    );
  }

  return (
    <div className="workflow-node" style={getNodeStyle()}>
      <Handle 
        type="target" 
        position={Position.Left} 
        className="node-handle target-handle"
        isConnectable={data.agent.category !== 'trigger'}
      />
      
      <div className="node-header">
        <div className="node-icon-wrapper">
          {isExecuting ? (
            <div className="loading-spinner"></div>
          ) : (
            <span className="node-icon">{data.agent.icon}</span>
          )}
        </div>
        
        <div className="node-info">
          <div className="node-name">{data.config?.name || data.agent.name}</div>
          <div className="node-type">{data.agent.category}</div>
        </div>

        <div className="node-actions">
          <button 
            className="node-action-btn configure"
            onClick={() => data.onConfigure?.(id)}
            title="Configure"
          >
            ⚙️
          </button>
          <button 
            className="node-action-btn delete"
            onClick={() => data.onDelete?.(id)}
            title="Delete"
          >
            🗑️
          </button>
        </div>
      </div>

      <div className="node-content">
        <p className="node-description">{data.agent.description}</p>
        
        {data.config && Object.keys(data.config).length > 0 && (
          <div className="node-config-preview">
            <div className="config-item">
              <span className="config-key">Status:</span>
              <span 
                className="config-value status"
                style={{ color: getExecutionStatusColor() }}
              >
                {executionStatus}
              </span>
            </div>
          </div>
        )}
      </div>

      <div className="node-footer">
        <button 
          className="execute-btn"
          onClick={handleExecute}
          disabled={isExecuting}
        >
          {isExecuting ? '⏳ Executing...' : '▶️ Test'}
        </button>
      </div>

      <div 
        className="execution-indicator"
        style={{ backgroundColor: getExecutionStatusColor() }}
      ></div>

      <Handle 
        type="source" 
        position={Position.Right} 
        className="node-handle source-handle"
      />
    </div>
  );
}

const nodeTypes = {
  workflowNode: WorkflowNode,
};

export default function DynamicWorkflowBuilder() {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isAgentSelectorOpen, setIsAgentSelectorOpen] = useState(false);
  const [newNodePosition, setNewNodePosition] = useState({ x: 0, y: 0 });
  const [configurationModal, setConfigurationModal] = useState({ 
    isOpen: false, 
    node: null 
  });
  const [workflowStatus, setWorkflowStatus] = useState('Draft');

  const onConnect = useCallback((connection) => {
    const edge = { 
      ...connection, 
      animated: true,
      style: { strokeWidth: 2 }
    };
    setEdges((eds) => addEdge(edge, eds));
  }, [setEdges]);

  // Add new node
  const addNode = (position = null) => {
    const pos = position || { 
      x: 250 + Math.random() * 400, 
      y: 100 + Math.random() * 300 
    };
    setNewNodePosition(pos);
    setIsAgentSelectorOpen(true);
  };

  // Create node with selected agent
  const createNodeWithAgent = (agent, position) => {
    const newNodeId = `node-${Date.now()}`;
    const newNode = {
      id: newNodeId,
      type: 'workflowNode',
      position,
      data: {
        agent,
        config: { ...agent.config },
        status: 'idle',
        onDelete: deleteNode,
        onConfigure: openConfiguration,
        onStatusChange: updateNodeStatus
      },
    };
    
    setNodes((nds) => [...nds, newNode]);
  };

  // Delete node
  const deleteNode = useCallback((nodeId) => {
    if (window.confirm('Delete this node?')) {
      setNodes((nodes) => nodes.filter((node) => node.id !== nodeId));
      setEdges((edges) => 
        edges.filter((edge) => 
          edge.source !== nodeId && edge.target !== nodeId
        )
      );
    }
  }, [setNodes, setEdges]);

  // Open configuration modal
  const openConfiguration = (nodeId) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      setConfigurationModal({ isOpen: true, node });
    }
  };

  // Save node configuration
  const saveNodeConfiguration = (nodeId, config) => {
    setNodes((nodes) => 
      nodes.map((node) => 
        node.id === nodeId 
          ? { 
              ...node, 
              data: { 
                ...node.data, 
                config 
              } 
            }
          : node
      )
    );
  };

  // Update node status
  const updateNodeStatus = (nodeId, status) => {
    setNodes((nodes) => 
      nodes.map((node) => 
        node.id === nodeId 
          ? { 
              ...node, 
              data: { 
                ...node.data, 
                status 
              } 
            }
          : node
      )
    );
  };

  // Execute entire workflow
  const executeWorkflow = async () => {
    setWorkflowStatus('Executing');
    
    // Simulate workflow execution
    const triggerNodes = nodes.filter(n => n.data.agent?.category === 'trigger');
    
    for (const node of triggerNodes) {
      updateNodeStatus(node.id, 'executing');
      await new Promise(resolve => setTimeout(resolve, 1000));
      updateNodeStatus(node.id, 'success');
    }
    
    setWorkflowStatus('Completed');
    setTimeout(() => setWorkflowStatus('Draft'), 3000);
  };

  // Clear workflow
  const clearWorkflow = () => {
    if (window.confirm('Clear entire workflow?')) {
      setNodes([]);
      setEdges([]);
    }
  };

  // Load workflow template
  const loadTemplate = (templateName) => {
    const templates = {
      'email-automation': [
        {
          id: '1',
          type: 'workflowNode',
          position: { x: 100, y: 200 },
          data: {
            agent: AGENT_TEMPLATES.triggers[2], // Email trigger
            config: { ...AGENT_TEMPLATES.triggers[2].config },
            status: 'idle',
            onDelete: deleteNode,
            onConfigure: openConfiguration,
            onStatusChange: updateNodeStatus
          }
        },
        {
          id: '2',
          type: 'workflowNode',
          position: { x: 400, y: 200 },
          data: {
            agent: AGENT_TEMPLATES.actions[0], // Send email
            config: { ...AGENT_TEMPLATES.actions[0].config },
            status: 'idle',
            onDelete: deleteNode,
            onConfigure: openConfiguration,
            onStatusChange: updateNodeStatus
          }
        }
      ]
    };
    
    const template = templates[templateName];
    if (template) {
      setNodes(template);
      setEdges([{ id: 'e1-2', source: '1', target: '2', animated: true }]);
    }
  };

  return (
    <div className="workflow-builder">
      <div className="workflow-header">
        <div className="header-left">
          <h2>Workflow Builder</h2>
          <span className={`workflow-status ${workflowStatus.toLowerCase()}`}>
            {workflowStatus}
          </span>
        </div>
        
        <div className="header-actions">
          <div className="template-dropdown">
            <select onChange={(e) => e.target.value && loadTemplate(e.target.value)}>
              <option value="">Load Template</option>
              <option value="email-automation">Email Automation</option>
            </select>
          </div>
          
          <button onClick={() => addNode()} className="btn secondary">
            ➕ Add Node
          </button>
          
          <button onClick={clearWorkflow} className="btn warning">
            🧹 Clear
          </button>
          
          <button 
            onClick={executeWorkflow} 
            className="btn primary"
            disabled={workflowStatus === 'Executing' || nodes.length === 0}
          >
            {workflowStatus === 'Executing' ? '⏳ Executing...' : '▶️ Execute'}
          </button>
        </div>
      </div>

      <div className="workflow-canvas">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          connectionLineType={ConnectionLineType.SmoothStep}
          fitView
          onPaneDoubleClick={(event) => {
            const rect = event.currentTarget.getBoundingClientRect();
            const position = {
              x: event.clientX - rect.left,
              y: event.clientY - rect.top
            };
            addNode(position);
          }}
        >
          <Background color="#f1f5f9" gap={20} />
          <Controls />
          <MiniMap 
            nodeColor={(node) => node.data.agent?.color || '#6b7280'}
            maskColor="rgba(255, 255, 255, 0.8)"
          />
        </ReactFlow>
      </div>

      <AgentSelector
        isOpen={isAgentSelectorOpen}
        onClose={() => setIsAgentSelectorOpen(false)}
        onSelectAgent={createNodeWithAgent}
        position={newNodePosition}
      />

      <ConfigurationModal
        node={configurationModal.node}
        isOpen={configurationModal.isOpen}
        onClose={() => setConfigurationModal({ isOpen: false, node: null })}
        onSave={saveNodeConfiguration}
      />
    </div>
  );
}
