import React, { createContext, useContext, useEffect, useState } from "react";
import { getCookie } from "../utils/Utils";
import { io } from "socket.io-client";
import { SOCKET_URL, BASE_URL, BASE_URL2 } from "../api/api";
import axios from "axios";
import { AuthContext } from "./AuthContext";
import { ChatContext } from "./ChatContext";
import { deleteCookie } from "../utils/Utils";
const AllContext = createContext();

const AllProvider = ({ children }) => {
  // const navigate= useNavigate();
  const [loading, setLoading] = useState(false);
  const { currentUser } = useContext(AuthContext);
  const { dispatch } = useContext(ChatContext);
  const [text, setText] = useState("");
  const [reply, setReply] = useState(null);
  const [socket, setSocket] = useState(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const [unReadChat, setUnReadChat] = useState([]);
  const [selectedMobileNumber, setSelectedMobileNumber] = useState(null);
  const [selectedUserDetails, setSelectedUserDetails] = useState({
    email: "",
    company: ""
  })
  const [chats, setChats] = useState([]);
  const [convpage, setConvPage] = useState(0);
  const [scrolarinmiddle, setScrolarinmiddle] = useState(false);
  const [page, setPage] = useState(0);
  const [allChats, setAllChats] = useState([]);
  const [starChats, setStarChats] = useState([]);
  const [channel, setChannel] = useState("");
  const [chatCategory, setChatCategory] = useState("all");
  const [chatFilterType, setChatFilterType] = useState({ value: "All Agents", label: "All Agents" });
  const [roleFilterType, setRoleFilterType] = useState("");
  const [nextConvId, setNextConvId] = useState(null);
  const [selectedChannel,setSelectedChannel]=useState('');
  const [unreadCount,setUnreadCount]=useState(0);
  const [waitingChats, setWaitingChats] = useState([]);
  const [filteredChats, setFilteredChats] = useState([]);
  const [checkboxList, setCheckboxList] = useState([]);
  const [chatsLoading, setChatsLoading] = useState(false);
  const [selectedName, setSelectedName] = useState("");
  const [isOldMsg, setIsOldMsg] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const [sendTemplatePopUp, setSendTemplatePopUp] = useState(false);
  const [wpProfile, setWpProfile] = useState([]);
  const [callData, setCallData] = useState({
    visible: false,
    name: "",
    mobile: null,
    content: "",
    message_type: "",
  });
  const [mobileVisible, setMobileVisible] = useState(false);
  const [breakInfo, setBreakInfo] = useState(null);
  const [pipelines, setPipelines] = useState([
    {
      id: '1',
      name: 'Leads',
      stages: [
        { id: '1-1', name: 'Contacted' },
        { id: '1-2', name: 'Qualified' },
      ],
    },
    {
      id: '2',
      name: 'Sales Funnel',
      stages: [
        { id: '2-1', name: 'Demo Scheduled' },
        { id: '2-2', name: 'Proposal Sent' },
        { id: '2-3', name: 'Negotiation' },
      ],
    },
    {
      id: '3',
      name: 'Partnerships',
      stages: [
        { id: '3-1', name: 'Outreach' },
        { id: '3-2', name: 'Call Booked' },
      ],
    },
  ]);

  const [activePipelineId, setActivePipelineId] = useState('');
  const [agents, setAgents] = useState([]);

  // Online Users WebSocket state
  const [onlineUsersSocket, setOnlineUsersSocket] = useState(null);
  const [onlineUsersData, setOnlineUsersData] = useState({
    totalOnline: 0,
    usersByCountry: [],
    lastUpdated: null,
    isConnected: false
  });

  useEffect(() => {
    const userCookie = getCookie("user");
    const userInfo = userCookie ? JSON.parse(userCookie) : null;
    const currentuser = userInfo?.data;
    const socketconn = io(SOCKET_URL);
    if (currentuser?.parent_id) {
      // console.log(currentuser);
      currentuser.item = "test"
      socketconn.emit("setup", currentuser);

      socketconn.on('user_logout', (data) => {
        if (currentUser.user_id === data.user_id && currentUser.user_type !== "admin") {
          deleteCookie("user");
        }
      })


      setSocket(socketconn);
      socketconn.on('user_not_found', (data) => {

        if (currentUser.user_id === data.user_id && currentUser.user_type !== "admin") {
          deleteCookie("user");
        }

      })
    }
    return () => {
      socketconn.off('user_not_found');
      socketconn.off('user_logout');
      socketconn.disconnect();
    };
  }, [currentUser]);

  // Online Users WebSocket connection
  useEffect(() => {
    if (!currentUser || !currentUser.parent_id) return;

    // WebSocket URL for online users tracking (from the widget implementation)
    const wsUrl = 'ws://localhost:8080';
    let ws = null;

    try {
      ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('🔌 Online Users WebSocket connected');
        setOnlineUsersData(prev => ({ ...prev, isConnected: true }));

        // Send dashboard connect message to receive real-time data
        ws.send(JSON.stringify({
          type: 'dashboard_connect',
          timestamp: Date.now()
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'dashboard_data') {
            setOnlineUsersData(prev => ({
              ...prev,
              totalOnline: data.totalOnline || 0,
              usersByCountry: data.usersByCountry || [],
              lastUpdated: data.timestamp || Date.now()
            }));
          }
        } catch (error) {
          console.error('Error parsing online users data:', error);
        }
      };

      ws.onclose = () => {
        console.log('🔌 Online Users WebSocket disconnected');
        setOnlineUsersData(prev => ({ ...prev, isConnected: false }));
      };

      ws.onerror = (error) => {
        console.error('Online Users WebSocket error:', error);
        setOnlineUsersData(prev => ({ ...prev, isConnected: false }));
      };

      setOnlineUsersSocket(ws);

    } catch (error) {
      console.error('Failed to connect to Online Users WebSocket:', error);
    }

    return () => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [currentUser]);

  useEffect(() => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id || currentUser.user_type !== "agent") return;
    const fetchAgentBreak = async () => {
      const payload = {
        user_id: currentUser.user_id,
        token: currentUser.token,
        method: "retrieve_agent_break",
        user_type: currentUser.user_type
      }
      try {
        const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent_setting`, payload)
        if (data.success && data.data) {

          const breakInfo = {
            active: data.data.break_status === 1 ? true : false,
            breakUntil: data.data.break_until,
            mode: data.data.break_mode,
            id: data.data._id,
          }
          setBreakInfo(breakInfo);

        }

      } catch (error) {
        console.log(error);

      }
    }

    fetchAgentBreak();

  }, [currentUser])

  const handleChatOpen = async (item) => {
    setSelectedMobileNumber(item.mobile);
    setSelectedName(item.name);
    setLoading(true);
    setConvPage(0);
    setText("");

    let readdata = {
      ...item,
      brand_number: currentUser.brand_number,
      parent_id: currentUser.parent_id,
    };

    const givenDate = new Date(item.created);
    const currentDate = new Date();
    const time_23hrs_59min_ago = new Date(
      currentDate.getTime() - (23 * 60 * 60 * 1000 + 59 * 60 * 1000)
    );
    if (givenDate < time_23hrs_59min_ago) {
      setIsOldMsg(true);
    } else {
      setIsOldMsg(false);
    }
    try {
      const forconvdata = {
        token: currentUser.parent_token,
        user_id: currentUser.parent_id,
        method: "conv_list_new",
        brand_number: currentUser.brand_number,
        start: 0,
        from_mobile: item.mobile,
      };

      const res = await axios.post(
        `${BASE_URL}/netcore_conversation.php`,
        forconvdata
      );

      if (res.data.success === true) {
        // socket.emit("read", readdata);
        let updatedunreadchat = unReadChat.filter(
          (items, index) => items.read_status === 0
        );

        setUnReadChat(updatedunreadchat);

        const index = chats.findIndex(
          (selecteditem) => selecteditem.mobile === readdata.mobile
        );

        const unreadindex = updatedunreadchat.findIndex(
          (selecteditem) => selecteditem.mobile === readdata.mobile
        );

        if (unreadindex > -1) {
          const updatedUnreadItems = [...updatedunreadchat];
          updatedUnreadItems[index] = {
            ...updatedUnreadItems[index],

            read_status: 1,
          };

          updatedUnreadItems.sort(
            (a, b) => a.id - b.id
          );

          setUnReadChat(updatedUnreadItems);
        }

        if (index !== -1) {
          const updatedItems = [...chats];
          updatedItems[index] = {
            ...updatedItems[index],

            read_status: 1,
          };

          updatedItems.sort(
            (a, b) => new Date(b.created) - new Date(a.created)
          );

          setChats(updatedItems);
        }

        if (chatCategory === "all") {
          setUnReadChat((prevItems) =>
            prevItems.filter((items, index) => items.mobile !== readdata.mobile)
          );
        }

        let updatedconv = res.data.data;
        updatedconv.sort((a, b) => a.id - b.id);

        await dispatch({
          type: "CHANGE_USER",
          payload: {
            mobile: item.mobile,
            conversation: updatedconv,
            name: item.name,
          },
        });
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const startBreak = ({ type, breakUntil, id }) => {
    setBreakInfo({
      active: true,
      type,
      breakUntil,
      id
    });
  };

  // End the break manually or when time ends
  const endBreak = () => {
    setBreakInfo(null);

  };

  const fetchAgents = async () => {
    if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
    const payload = {
      user_id: currentUser.parent_id,
      method: "retrieve_agent",
      token: currentUser.parent_token,
      user_type: currentUser.user_type,
      agent_id: currentUser.user_id,
    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);

      if (data.success) {
        const agents = data.data.filter((item) => item.agent_type === "agent")
        const formattedAgentList = agents.map((item) => ({
          value: item.id,
          label: item.name
        }))

        setAgents([...formattedAgentList]);
      }

    } catch (error) {
      console.error("Error fetching agents:", error);

    }


  }

  return (
    <AllContext.Provider
      value={{
        isViewerOpen,
        setIsViewerOpen,
        selectedImage,
        setSelectedImage,
        unReadChat,
        setUnReadChat,
        selectedMobileNumber,
        setSelectedMobileNumber,
        chats,
        setChats,
        convpage,
        setConvPage,
        scrolarinmiddle,
        setScrolarinmiddle,
        page,
        setPage,
        allChats,
        setAllChats,
        starChats,
        setStarChats,
        checkboxList,
        setCheckboxList,
        chatsLoading,
        setChatsLoading,
        selectedName,
        setSelectedName,
        isOldMsg,
        setIsOldMsg,
        remainingTime,
        setRemainingTime,
        sendTemplatePopUp,
        setSendTemplatePopUp,
        wpProfile,
        setWpProfile,
        callData,
        setCallData,
        socket,
        text,
        setText,
        handleChatOpen,
        waitingChats,
        setWaitingChats,
        filteredChats,
        setFilteredChats,
        selectedUserDetails,
        setSelectedUserDetails,
        reply,
        setReply,
        mobileVisible,
        setMobileVisible,
        breakInfo,
        startBreak,
        endBreak,
        pipelines,
        setPipelines,
        activePipelineId,
        setActivePipelineId,
        fetchAgents,
        agents,
        channel,
        setChannel,
        chatCategory,
        setChatCategory,
        chatFilterType,
        setChatFilterType,
        roleFilterType,
        setRoleFilterType,
        nextConvId,
        setNextConvId,
        selectedChannel,
        setSelectedChannel,
        setUnreadCount,
        unreadCount,
        onlineUsersData,
        setOnlineUsersData
      }}
    >
      {children}
    </AllContext.Provider>
  );
};

export const ChatState = () => {
  return useContext(AllContext);
};

export default AllProvider;
