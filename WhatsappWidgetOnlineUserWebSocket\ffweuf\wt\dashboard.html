<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Analytics Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
            min-height: 100vh;
            padding: 20px;
            color: #ffffff;
        }
        
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(20, 20, 20, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ffffff, #a0a0a0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #9ca3af;
            font-weight: 400;
        }
        
        .status-indicator {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: #10b981;
            border-radius: 50%;
            margin-left: 12px;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { 
                opacity: 1;
                box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
            }
            50% { 
                opacity: 0.7;
                box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
            }
            100% { 
                opacity: 1;
                box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
            }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(20, 20, 20, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 40px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            border-color: rgba(120, 119, 198, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        }
        
        .total-users {
            text-align: center;
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(255, 119, 198, 0.05));
        }
        
        .total-users .number {
            font-size: 5rem;
            font-weight: 800;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #10b981, #06d6a0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
        }
        
        .total-users .label {
            font-size: 1.3rem;
            color: #9ca3af;
            font-weight: 500;
        }
        
        .country-stats {
            background: linear-gradient(135deg, rgba(120, 219, 255, 0.08), rgba(120, 119, 198, 0.05));
        }
        
        .country-stats h3 {
            margin-bottom: 24px;
            font-size: 1.6rem;
            font-weight: 600;
            color: #ffffff;
        }
        
        .country-list {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(120, 119, 198, 0.3) transparent;
        }
        
        .country-list::-webkit-scrollbar {
            width: 6px;
        }
        
        .country-list::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .country-list::-webkit-scrollbar-thumb {
            background: rgba(120, 119, 198, 0.3);
            border-radius: 3px;
        }
        
        .country-list::-webkit-scrollbar-thumb:hover {
            background: rgba(120, 119, 198, 0.5);
        }
        
        .country-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .country-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(120, 119, 198, 0.2);
            transform: translateX(4px);
        }
        
        .country-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, #10b981, #06d6a0);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .country-item:hover::before {
            opacity: 1;
        }
        
        .country-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: #ffffff;
        }
        
        .country-count {
            background: linear-gradient(135deg, #10b981, #06d6a0);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            min-width: 50px;
            text-align: center;
        }
        
        .last-updated {
            text-align: center;
            color: #6b7280;
            font-size: 0.95rem;
            margin-top: 30px;
            padding: 16px;
            background: rgba(20, 20, 20, 0.5);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .connection-status {
            position: fixed;
            top: 30px;
            right: 30px;
            padding: 12px 24px;
            border-radius: 30px;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .connected {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(6, 214, 160, 0.9));
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
        }
        
        .disconnected {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 127, 0.9));
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .stat-card {
            animation: slideIn 0.6s ease forwards;
        }
        
        .stat-card:nth-child(2) {
            animation-delay: 0.1s;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .total-users .number {
                font-size: 4rem;
            }
            
            .stat-card {
                padding: 30px 20px;
            }
            
            .connection-status {
                top: 20px;
                right: 20px;
                padding: 10px 20px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .total-users .number {
                font-size: 3rem;
            }
            
            .country-item {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1>User Analytics Dashboard <span class="status-indicator"></span></h1>
            <p>Real-time user tracking and analytics</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card total-users">
                <div class="number" id="total-count">0</div>
                <div class="label">Users Currently Online</div>
            </div>
            
            <div class="stat-card country-stats">
                <h3>Currently Online Users by Country</h3>
                <div class="country-list" id="country-list">
                    <div class="country-item">
                        <span class="country-name">Loading...</span>
                        <span class="country-count">0</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="last-updated" id="last-updated">
            Last updated: Never
        </div>
    </div>
    
    <div class="connection-status disconnected" id="connection-status">
        Disconnected
    </div>

    <script>
        class Dashboard {
            constructor() {
                this.wsUrl = 'ws://localhost:8080';
                this.ws = null;
                this.isConnected = false;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                
                this.totalCountElement = document.getElementById('total-count');
                this.countryListElement = document.getElementById('country-list');
                this.lastUpdatedElement = document.getElementById('last-updated');
                this.connectionStatusElement = document.getElementById('connection-status');
                
                this.initWebSocket();
            }
            
            initWebSocket() {
                try {
                    console.log('🔌 Connecting to WebSocket...');
                    this.ws = new WebSocket(this.wsUrl);
                    
                    this.ws.onopen = () => {
                        console.log('✅ Dashboard WebSocket connected');
                        this.isConnected = true;
                        this.reconnectAttempts = 0;
                        this.updateConnectionStatus(true);
                        
                        // Identify as dashboard client
                        this.ws.send(JSON.stringify({
                            type: 'dashboard_connect'
                        }));
                    };
                    
                    this.ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleMessage(data);
                        } catch (error) {
                            console.warn('Invalid WebSocket message:', error);
                        }
                    };
                    
                    this.ws.onclose = () => {
                        console.log('❌ Dashboard WebSocket disconnected');
                        this.isConnected = false;
                        this.updateConnectionStatus(false);
                        this.handleReconnect();
                    };
                    
                    this.ws.onerror = (error) => {
                        console.warn('WebSocket error:', error);
                        this.isConnected = false;
                        this.updateConnectionStatus(false);
                    };
                    
                } catch (error) {
                    console.warn('WebSocket initialization failed:', error);
                    this.updateConnectionStatus(false);
                }
            }
            
            handleMessage(data) {
                if (data.type === 'dashboard_data') {
                    this.updateDashboard(data);
                }
            }
            
            updateDashboard(data) {
                // Update total online users count with animation
                this.animateNumberChange(this.totalCountElement, data.totalOnline);
                
                // Update country list
                this.updateCountryList(data.usersByCountry);
                
                const dataType = data.dataType === 'real-time' ? 'Real-time' : 'Historical';
                const now = new Date(data.timestamp);
                this.lastUpdatedElement.textContent = `${dataType} data - Last updated: ${now.toLocaleTimeString()}`;
                
                console.log('📊 Dashboard updated with real-time data:', data);
            }
            
            animateNumberChange(element, newValue) {
                const currentValue = parseInt(element.textContent) || 0;
                const difference = newValue - currentValue;
                const steps = 20;
                const increment = difference / steps;
                let current = currentValue;
                
                const animation = setInterval(() => {
                    current += increment;
                    if ((increment > 0 && current >= newValue) || (increment < 0 && current <= newValue)) {
                        current = newValue;
                        clearInterval(animation);
                    }
                    element.textContent = Math.round(current);
                }, 50);
            }
            
            updateCountryList(countries) {
                if (!countries || countries.length === 0) {
                    this.countryListElement.innerHTML = `
                        <div class="country-item">
                            <span class="country-name">No users online</span>
                            <span class="country-count">0</span>
                        </div>
                    `;
                    return;
                }
                
                const countryHTML = countries.map((country, index) => `
                    <div class="country-item" style="animation-delay: ${index * 0.05}s">
                        <span class="country-name">${country.country}</span>
                        <span class="country-count">${country.user_count}</span>
                    </div>
                `).join('');
                
                this.countryListElement.innerHTML = countryHTML;
            }
            
            updateConnectionStatus(connected) {
                this.connectionStatusElement.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
                this.connectionStatusElement.textContent = connected ? '● Connected' : '● Disconnected';
            }
            
            handleReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
                    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
                    
                    setTimeout(() => {
                        this.initWebSocket();
                    }, delay);
                } else {
                    console.log('❌ Max reconnection attempts reached');
                }
            }
            
            cleanup() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.close();
                }
            }
        }
        
        // Initialize dashboard
        const dashboard = new Dashboard();
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            dashboard.cleanup();
        });
    </script>
</body>
</html>
